# Token Management System

## Overview
Sistem manajemen token telah diimplementasikan untuk menangani autentikasi user dengan token JWT dari backend. Sistem ini menyimpan token dan data user di localStorage dan menyediakan utility functions untuk mengelola state autentikasi.

## Files yang Terlibat

### 1. `src/utils/auth.js`
File utility utama yang mengelola autentikasi:
- `tokenManager`: Mengelola token JWT
- `userManager`: Mengelola data user
- `authManager`: Mengelola state autentikasi secara keseluruhan

### 2. `src/services/api.js`
Service API yang telah diperbaiki untuk:
- Mengirim request login ke backend
- Menyediakan helper functions untuk auth headers
- Mendukung authenticated requests

### 3. `src/pages/login.jsx`
Halaman login yang telah diupdate untuk:
- Menggunakan `authManager` untuk menyimpan token
- Menampilkan logging yang lebih baik
- Mengintegrasikan AuthDebug component

### 4. `src/components/AuthDebug.jsx`
Component debug untuk development yang menampilkan:
- Status autentikasi
- Token yang tersimpan
- Data user
- Tombol logout

## Cara Kerja

### Login Process
1. User mengisi form login (email & password)
2. Data dikirim ke backend via `apiService.login()`
3. Backend mengembalikan response dengan format:
   ```json
   {
     "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
     "user": {
       "id": "user_id",
       "email": "<EMAIL>",
       "name": "User Name",
       "verified": false,
       ...
     }
   }
   ```
4. `authManager.login()` menyimpan token dan user data ke localStorage
5. User diarahkan ke halaman home

### Token Storage
- **Token**: Disimpan di `localStorage` dengan key `"authToken"`
- **User Data**: Disimpan di `localStorage` dengan key `"user"` dalam format JSON

### Authentication Check
```javascript
import authManager from '../utils/auth.js';

// Check if user is authenticated
const isLoggedIn = authManager.isAuthenticated();

// Get current user data
const currentUser = authManager.getAuthState().user;

// Get token for API requests
const token = authManager.getAuthState().token;
```

### Logout
```javascript
import authManager from '../utils/auth.js';

// Logout user (clears token and user data)
authManager.logout();
```

### Authenticated API Requests
```javascript
import { apiService } from '../services/api.js';

// Example authenticated request
const userProfile = await apiService.getProfile();
```

## Testing & Debugging

### AuthDebug Component
Untuk testing dan debugging, gunakan `AuthDebug` component yang menampilkan:
- Status autentikasi real-time
- Token yang tersimpan (dengan opsi copy)
- Data user yang tersimpan
- Tombol logout untuk testing

### Console Logging
Sistem ini menyediakan logging yang detail di browser console:
- Login process
- Token storage
- User data storage
- Logout process

### Manual Testing
1. Buka halaman login
2. Klik tombol "🔍 Auth Debug" di pojok kanan bawah
3. Login dengan credentials yang valid
4. Periksa AuthDebug panel untuk melihat token dan user data
5. Periksa browser console untuk logging detail
6. Periksa localStorage di Developer Tools:
   - Key: `authToken` - berisi JWT token
   - Key: `user` - berisi JSON user data

## API Integration

### Backend Response Format
Backend harus mengembalikan response dengan format:
```json
{
  "token": "JWT_TOKEN_HERE",
  "user": {
    "id": "user_id",
    "email": "<EMAIL>",
    "name": "User Name",
    "verified": boolean,
    // ... other user fields
  }
}
```

### Error Handling
Sistem menangani berbagai error scenarios:
- Network errors
- Invalid credentials
- Missing token in response
- Malformed response data

## Security Considerations

1. **Token Storage**: Token disimpan di localStorage (bukan sessionStorage) sehingga persist across browser sessions
2. **Token Validation**: Basic JWT format validation (3 parts separated by dots)
3. **Auto-cleanup**: Logout function membersihkan semua auth data
4. **Error Handling**: Sensitive error information tidak ditampilkan ke user

## Next Steps

1. **Token Refresh**: Implementasi automatic token refresh
2. **Token Expiry**: Handle token expiration
3. **Secure Storage**: Consider more secure storage options
4. **Route Protection**: Implement protected routes based on auth state
5. **Role-based Access**: Extend system for user roles/permissions
