import { useState, useEffect } from "react";
import authManager from "../utils/auth.js";

export default function AuthDebug() {
  const [authState, setAuthState] = useState(null);
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    // Update auth state
    const updateAuthState = () => {
      setAuthState(authManager.getAuthState());
    };

    updateAuthState();

    // Listen for storage changes
    const handleStorageChange = () => {
      updateAuthState();
    };

    window.addEventListener('storage', handleStorageChange);
    
    // Also check periodically in case of same-tab changes
    const interval = setInterval(updateAuthState, 1000);

    return () => {
      window.removeEventListener('storage', handleStorageChange);
      clearInterval(interval);
    };
  }, []);

  const handleLogout = () => {
    authManager.logout();
    setAuthState(authManager.getAuthState());
  };

  const copyToClipboard = (text) => {
    navigator.clipboard.writeText(text).then(() => {
      alert('Copied to clipboard!');
    });
  };

  if (!isVisible) {
    return (
      <div className="fixed bottom-4 right-4 z-50">
        <button
          onClick={() => setIsVisible(true)}
          className="bg-blue-500 text-white px-3 py-2 rounded-lg shadow-lg hover:bg-blue-600 transition-colors text-sm"
        >
          🔍 Auth Debug
        </button>
      </div>
    );
  }

  return (
    <div className="fixed bottom-4 right-4 z-50 bg-white border border-gray-300 rounded-lg shadow-xl p-4 max-w-md max-h-96 overflow-auto">
      <div className="flex justify-between items-center mb-3">
        <h3 className="font-bold text-gray-800">Auth Debug Info</h3>
        <button
          onClick={() => setIsVisible(false)}
          className="text-gray-500 hover:text-gray-700 text-xl"
        >
          ×
        </button>
      </div>

      {authState && (
        <div className="space-y-3 text-sm">
          <div>
            <strong className="text-gray-700">Status:</strong>
            <span className={`ml-2 px-2 py-1 rounded text-xs ${
              authState.isAuthenticated 
                ? 'bg-green-100 text-green-800' 
                : 'bg-red-100 text-red-800'
            }`}>
              {authState.isAuthenticated ? 'Authenticated' : 'Not Authenticated'}
            </span>
          </div>

          <div>
            <strong className="text-gray-700">Token:</strong>
            {authState.token ? (
              <div className="mt-1">
                <div className="bg-gray-100 p-2 rounded text-xs font-mono break-all">
                  {authState.token.substring(0, 50)}...
                </div>
                <button
                  onClick={() => copyToClipboard(authState.token)}
                  className="mt-1 text-blue-500 hover:text-blue-700 text-xs"
                >
                  Copy Full Token
                </button>
              </div>
            ) : (
              <span className="ml-2 text-gray-500">No token</span>
            )}
          </div>

          <div>
            <strong className="text-gray-700">User Data:</strong>
            {authState.user ? (
              <div className="mt-1">
                <div className="bg-gray-100 p-2 rounded text-xs">
                  <div><strong>Email:</strong> {authState.user.email}</div>
                  <div><strong>Name:</strong> {authState.user.name}</div>
                  <div><strong>ID:</strong> {authState.user.id}</div>
                  <div><strong>Verified:</strong> {authState.user.verified ? 'Yes' : 'No'}</div>
                </div>
                <button
                  onClick={() => copyToClipboard(JSON.stringify(authState.user, null, 2))}
                  className="mt-1 text-blue-500 hover:text-blue-700 text-xs"
                >
                  Copy User Data
                </button>
              </div>
            ) : (
              <span className="ml-2 text-gray-500">No user data</span>
            )}
          </div>

          {authState.isAuthenticated && (
            <button
              onClick={handleLogout}
              className="w-full bg-red-500 text-white py-2 px-3 rounded hover:bg-red-600 transition-colors text-sm"
            >
              Logout
            </button>
          )}
        </div>
      )}
    </div>
  );
}
