import { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../hooks/useAuth.js';

/**
 * Component to protect routes that require authentication
 * @param {Object} props
 * @param {React.ReactNode} props.children - Child components to render if authenticated
 * @param {string} props.redirectTo - Path to redirect to if not authenticated (default: '/login')
 * @param {React.ReactNode} props.fallback - Component to show while loading
 */
export default function ProtectedRoute({ 
  children, 
  redirectTo = '/login', 
  fallback = <div>Loading...</div> 
}) {
  const { isAuthenticated, isLoading } = useAuth();
  const navigate = useNavigate();

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      navigate(redirectTo);
    }
  }, [isLoading, isAuthenticated, navigate, redirectTo]);

  if (isLoading) {
    return fallback;
  }

  if (!isAuthenticated) {
    return null; // Will redirect via useEffect
  }

  return children;
}

/**
 * Higher-order component version of ProtectedRoute
 * @param {React.Component} Component - Component to wrap
 * @param {Object} options - Protection options
 * @returns {React.Component} Protected component
 */
export function withAuth(Component, options = {}) {
  return function ProtectedComponent(props) {
    return (
      <ProtectedRoute {...options}>
        <Component {...props} />
      </ProtectedRoute>
    );
  };
}
