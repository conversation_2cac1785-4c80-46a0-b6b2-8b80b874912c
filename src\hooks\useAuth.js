import { useState, useEffect } from 'react';
import authManager from '../utils/auth.js';

/**
 * Custom hook for managing authentication state
 * @returns {Object} Auth state and methods
 */
export function useAuth() {
  const [authState, setAuthState] = useState({
    isAuthenticated: false,
    user: null,
    token: null,
    isLoading: true
  });

  useEffect(() => {
    // Initialize auth state
    const updateAuthState = () => {
      const currentAuthState = authManager.getAuthState();
      setAuthState({
        ...currentAuthState,
        isLoading: false
      });
    };

    updateAuthState();

    // Listen for storage changes (for multi-tab sync)
    const handleStorageChange = (e) => {
      if (e.key === 'authToken' || e.key === 'user') {
        updateAuthState();
      }
    };

    window.addEventListener('storage', handleStorageChange);

    return () => {
      window.removeEventListener('storage', handleStorageChange);
    };
  }, []);

  const login = (response) => {
    const success = authManager.login(response);
    if (success) {
      setAuthState({
        ...authManager.getAuthState(),
        isLoading: false
      });
    }
    return success;
  };

  const logout = () => {
    authManager.logout();
    setAuthState({
      isAuthenticated: false,
      user: null,
      token: null,
      isLoading: false
    });
  };

  return {
    ...authState,
    login,
    logout
  };
}

/**
 * Hook to require authentication - redirects to login if not authenticated
 * @param {string} redirectTo - Path to redirect to after login
 * @returns {Object} Auth state
 */
export function useRequireAuth(redirectTo = '/') {
  const auth = useAuth();
  const [shouldRedirect, setShouldRedirect] = useState(false);

  useEffect(() => {
    if (!auth.isLoading && !auth.isAuthenticated) {
      setShouldRedirect(true);
    }
  }, [auth.isLoading, auth.isAuthenticated]);

  return {
    ...auth,
    shouldRedirect
  };
}
