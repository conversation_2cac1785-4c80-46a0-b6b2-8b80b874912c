import { useState } from "react";
import { Link, useNavigate } from "react-router-dom";
import AuthDebug from "../components/AuthDebug.jsx";
import { apiService, formatLoginData } from "../services/api.js";
import authManager from "../utils/auth.js";

export default function Login() {
  const [form, setForm] = useState({ email: "", password: "" });
  const [error, setError] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const navigate = useNavigate();

  const handleChange = (e) => {
    const { name, value } = e.target;
    setForm((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError("");
    setIsLoading(true);

    // Simple validation
    if (!form.email || !form.password) {
      setError("Please fill in all fields");
      setIsLoading(false);
      return;
    }

    try {
      // Prepare data for API according to the backend format
      const loginData = formatLoginData(form);
      console.log("Sending login data:", loginData);

      const response = await apiService.login(loginData);
      console.log("Login response received:", response);

      // Use authManager to handle login
      const loginSuccess = authManager.login(response);

      if (loginSuccess) {
        console.log(
          "Login successful! Auth state:",
          authManager.getAuthState()
        );

        // Navigate to home page
        navigate("/");
        window.location.reload(); // Refresh to update navbar
      } else {
        console.error("Login failed - no token in response:", response);
        setError("Login failed: No authentication token received from server");
        setIsLoading(false);
        return;
      }
    } catch (error) {
      console.error("Login error:", error);
      setError(
        error.message ||
          "Network error. Please check your connection and try again."
      );
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <>
      <div className="min-h-screen bg-gradient-to-br from-ubud-dark-green to-ubud-light-green flex items-center justify-center p-4">
        <div className="bg-white/10 backdrop-blur-sm rounded-2xl shadow-2xl flex w-full max-w-4xl overflow-hidden">
          {/* Left Side - Form */}
          <div className="flex-1 p-8 flex flex-col justify-center">
            <div className="max-w-sm mx-auto w-full">
              <h2 className="text-3xl font-bold text-white mb-2">
                Welcome Back!
              </h2>
              <p className="text-ubud-cream mb-6">
                Sign in to your account to continue
              </p>

              {error && (
                <div className="bg-red-500/20 border border-red-400 text-red-100 px-4 py-3 rounded-lg mb-4">
                  {error}
                </div>
              )}

              <form onSubmit={handleSubmit} className="space-y-4">
                <div>
                  <input
                    type="email"
                    name="email"
                    value={form.email}
                    onChange={handleChange}
                    placeholder="Email address"
                    className="w-full px-4 py-3 rounded-lg bg-white/20 border border-white/30 text-white placeholder-white/70 focus:outline-none focus:ring-2 focus:ring-ubud-yellow focus:border-transparent"
                    required
                  />
                </div>

                <div>
                  <input
                    type="password"
                    name="password"
                    value={form.password}
                    onChange={handleChange}
                    placeholder="Password"
                    className="w-full px-4 py-3 rounded-lg bg-white/20 border border-white/30 text-white placeholder-white/70 focus:outline-none focus:ring-2 focus:ring-ubud-yellow focus:border-transparent"
                    required
                  />
                </div>

                <div className="flex items-center justify-between text-ubud-cream text-sm">
                  <label className="flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      className="mr-2 accent-ubud-yellow"
                    />
                    Remember me
                  </label>
                  <a
                    href="#"
                    className="hover:text-ubud-yellow transition-colors"
                  >
                    Forgot password?
                  </a>
                </div>

                <button
                  type="submit"
                  disabled={isLoading}
                  className="w-full bg-ubud-yellow text-ubud-dark-green font-bold py-3 rounded-lg hover:bg-yellow-400 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isLoading ? "Signing In..." : "Sign In"}
                </button>
              </form>

              <div className="mt-6 text-center">
                <span className="text-ubud-cream">Don't have an account? </span>
                <Link
                  to="/register"
                  className="text-ubud-yellow font-bold hover:text-yellow-400 transition-colors"
                >
                  Sign Up
                </Link>
              </div>
            </div>
          </div>

          {/* Right Side - Image */}
          <div className="hidden lg:block flex-1 relative">
            <div className="absolute inset-0 bg-gradient-to-br from-ubud-yellow/20 to-transparent"></div>
            <div
              className="h-full bg-cover bg-center"
              style={{
                backgroundImage:
                  "url('https://images.unsplash.com/photo-**********-392fe2489ffa?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80')",
              }}
            ></div>
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="text-center text-white">
                <h3 className="text-2xl font-bold mb-2">Explore Ubud</h3>
                <p className="text-ubud-cream">Adventure awaits you</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Auth Debug Component - only visible in development */}
      <AuthDebug />
    </>
  );
}
