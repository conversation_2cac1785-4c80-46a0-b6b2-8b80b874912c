// Authentication utility functions

// Token management
export const tokenManager = {
  // Get token from localStorage
  getToken: () => {
    return localStorage.getItem("authToken");
  },

  // Set token in localStorage
  setToken: (token) => {
    if (token) {
      localStorage.setItem("authToken", token);
      console.log("Token stored successfully");
    }
  },

  // Remove token from localStorage
  removeToken: () => {
    localStorage.removeItem("authToken");
    console.log("Token removed");
  },

  // Check if token exists
  hasToken: () => {
    return !!localStorage.getItem("authToken");
  },

  // Validate token format (basic JWT check)
  isValidTokenFormat: (token) => {
    if (!token) return false;
    const parts = token.split('.');
    return parts.length === 3; // JWT has 3 parts separated by dots
  }
};

// User data management
export const userManager = {
  // Get user data from localStorage
  getUser: () => {
    const userData = localStorage.getItem("user");
    try {
      return userData ? JSON.parse(userData) : null;
    } catch (error) {
      console.error("Error parsing user data:", error);
      return null;
    }
  },

  // Set user data in localStorage
  setUser: (userData) => {
    if (userData) {
      localStorage.setItem("user", JSON.stringify(userData));
      console.log("User data stored successfully:", userData);
    }
  },

  // Remove user data from localStorage
  removeUser: () => {
    localStorage.removeItem("user");
    console.log("User data removed");
  },

  // Check if user data exists
  hasUser: () => {
    return !!localStorage.getItem("user");
  }
};

// Authentication state management
export const authManager = {
  // Check if user is authenticated
  isAuthenticated: () => {
    const token = tokenManager.getToken();
    const user = userManager.getUser();
    return !!(token && user);
  },

  // Login - store token and user data
  login: (response) => {
    try {
      if (response.token) {
        tokenManager.setToken(response.token);
        
        if (response.user) {
          userManager.setUser(response.user);
        } else {
          // If user data is in the root of response
          const { token, ...userData } = response;
          userManager.setUser(userData);
        }
        
        console.log("Login successful - Token and user data stored");
        return true;
      } else {
        console.error("No token found in login response");
        return false;
      }
    } catch (error) {
      console.error("Error during login process:", error);
      return false;
    }
  },

  // Logout - clear all auth data
  logout: () => {
    tokenManager.removeToken();
    userManager.removeUser();
    console.log("Logout successful - All auth data cleared");
  },

  // Get current auth state
  getAuthState: () => {
    return {
      isAuthenticated: authManager.isAuthenticated(),
      token: tokenManager.getToken(),
      user: userManager.getUser()
    };
  }
};

// Export default auth manager for convenience
export default authManager;
